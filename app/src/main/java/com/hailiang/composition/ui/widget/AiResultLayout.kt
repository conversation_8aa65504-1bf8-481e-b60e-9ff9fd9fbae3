package com.hailiang.composition.ui.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.hailiang.composition.data.bean.CompositionCheckBean
import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.composition.ui.practice.AiResponseState
import com.hailiang.composition.ui.practice.AiSector
import com.hailiang.composition.ui.practice.AiSectorComment
import com.hailiang.composition.ui.practice.AiSubSector
import com.hailiang.composition.ui.practice.EvaluateFeedback
import com.hailiang.composition.ui.practice.EvaluateFeedbackState
import com.hailiang.composition.ui.practice.theme.CompositionFonts
import com.hailiang.markdown.MarkDownCommentView
import com.hailiang.xxb.composition.R

/**
 * Description: AI 识别结果：综合评价、点拨、加油站
 *
 * <AUTHOR>
 * @version 2025/4/11 10:55
 */
@Composable
fun AiResultLayout(
    aiResponseState: AiResponseState,
    aiTabList: List<AiSector>,
    switchAiTab: (AiSector) -> Unit,
    retry: () -> Unit,
    reload: () -> Unit,
    retake: (() -> Unit)? = null,
    footer: (@Composable () -> Unit)? = null,
    bottomBar: @Composable () -> Unit,
    sentenceClickIndex: Int = -1,
    initFeedback: EvaluateFeedbackState? = null,
    feedbackClick: ((AiSector, EvaluateFeedback, Boolean) -> Unit)? = null,
) {
    when (aiResponseState) {
        is AiResponseState.Default -> {
            LoadingAnimation()
        }

        is AiResponseState.Success -> {
            AiSuccessLayout(
                sectorList = aiTabList,
                selectedSector = aiResponseState.selectedAiSector,
                sectorJobStatus = aiResponseState.selectedJobStatus,
                scoreJobStatus = aiResponseState.scoreJobStatus,
                subSectors = aiResponseState.aiSubSectorList,
                comprehensiveJudge = aiResponseState.comprehensiveJudge,
                adviceList = aiResponseState.adviceList,
                allusionList = aiResponseState.allusionList,
                score = aiResponseState.score,
                switchAiTab = switchAiTab,
                allSuccess = if (aiResponseState.selectedJobStatus.isSuccess() && aiResponseState.scoreJobStatus.isSuccess() && aiResponseState.allJobStatus.isSuccess()) true else false,
                footer = footer,
                bottomBar = bottomBar,
                sentenceClickIndex = sentenceClickIndex,
                initFeedback = initFeedback,
                feedbackClick = feedbackClick,
            )
        }

        is AiResponseState.Reload -> {
            ReloadPlaceHolder(modifier = Modifier.fillMaxSize(), reload = reload)
        }

        is AiResponseState.Retry -> {
            ReloadPlaceHolder(modifier = Modifier.fillMaxSize(), reload = retry)
        }

        is AiResponseState.AiRequestError -> {
            AiSteamLayout(aiStreamDetail = aiResponseState.aiStreamDetail, doRetry = reload)
        }

        is AiResponseState.AiStreamError -> {
            AiSteamLayout(aiStreamDetail = aiResponseState.aiStreamDetail, doRetry = {
                if (aiResponseState.aiStreamDetail.isNetworkError()) {
                    reload()
                } else {
                    retry()
                }
            })
        }

        is AiResponseState.AiStreaming -> {
            AiSteamLayout(aiStreamDetail = aiResponseState.aiStreamDetail, doRetry = {
                if (aiResponseState.aiStreamDetail.isNetworkError()) {
                    reload()
                } else {
                    retry()
                }
            })
        }

        is AiResponseState.OcrLoading -> {
            if (!aiResponseState.isTopic) {
                OcrLoadingAnimation(progress = aiResponseState.progress)
            }
        }

        is AiResponseState.Loading -> {
            AiLoadingAnimation()
        }

        is AiResponseState.ContentOcrFailed -> {
            RetakePlaceHolder(
                modifier = Modifier.fillMaxSize(),
                retake = retake ?: {},
                errorMessage = aiResponseState.errorMessage
                    ?: "作文识别失败，请上传清晰、完整的图片哦～"
            )
        }

        is AiResponseState.TitleOcrFailed -> {
            RetakePlaceHolder(
                modifier = Modifier.fillMaxSize(),
                retake = retake ?: {},
                errorMessage = aiResponseState.errorMessage
                    ?: "题目识别失败，请上传清晰、完整的图片哦～"
            )
        }
    }
}


/**
 * 识别成功
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AiSuccessLayout(
    sectorList: List<AiSector>,
    selectedSector: AiSector,
    sectorJobStatus: JobStatus,
    scoreJobStatus: JobStatus,
    subSectors: List<AiSubSector>,
    score: Int,
    switchAiTab: (AiSector) -> Unit,
    footer: (@Composable () -> Unit)? = null,
    bottomBar: @Composable () -> Unit,
    allusionList: List<CompositionCheckBean.Allusion>?,
    adviceList: List<CompositionCheckBean.Advice>?,
    comprehensiveJudge: CompositionCheckBean.ComprehensiveJudge?,
    allSuccess: Boolean,
    sentenceClickIndex: Int = -1,
    initFeedback: EvaluateFeedbackState? = null,
    feedbackClick: ((AiSector, EvaluateFeedback, Boolean) -> Unit)? = null,
) {
    Scaffold(
        bottomBar = bottomBar
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(color = Color.White)
                .padding(innerPadding)
        ) {
            Column(
                modifier = Modifier
                    .wrapContentSize()
                    .padding(horizontal = 20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                CompositionAiTabLayout(
                    selectedSector = selectedSector,
                    onClick = switchAiTab,
                    sectorList = sectorList
                )

                Spacer(modifier = Modifier.size(20.dp))

                if (allSuccess) {
                    val mergeList = sectorList.map { aiSector ->
                        aiSector.getAiSubSectorList(comprehensiveJudge, adviceList, allusionList)
                            .filter {
                                it.comments.isNotEmpty() || it.placeHolder != null
                            }
                    }
                    AiMergeList(
                        sectorList = sectorList,
                        aiSector = selectedSector,
                        subSectorsList = mergeList,
                        footer = footer,
                        switchAiTab = switchAiTab,
                        sentenceClickIndex = sentenceClickIndex,
                        initFeedback = initFeedback,
                        feedbackClick = feedbackClick,
                        score = if (scoreJobStatus == JobStatus.SUCCESS) score else -1
                    )
                } else {
                    AiSectorList(
                        jobStatus = sectorJobStatus,
                        aiSector = selectedSector,
                        subSectors = subSectors.filter { // 有内容或者占位符才需要显示
                            it.comments.isNotEmpty() || it.placeHolder != null
                        },
                        footer = footer,
                        score = if (scoreJobStatus == JobStatus.SUCCESS) score else -1
                    )
                }
            }
        }
    }
}

/**
 * 综合评价中的分数
 */
@Composable
private fun ScoreLayout(score: Int) {
    Row(
        modifier = Modifier
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "预估分",
            color = Color(0xFF17233D),
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold
        )
        if (score >= 0) {
            Text(
                text = "$score",
                fontSize = 18.sp,
                color = Color(0xFFEB5848)
            )
        } else {
            Text(
                text = "生成中...",
                fontSize = 18.sp,
                color = Color(0xFF99A3FF)
            )
        }
        Text(
            text = "(满分60)",
            fontSize = 18.sp,
            color = Color(0xFF515A6E)
        )
    }
}

@Composable
fun AiMergeList(
    sectorList: List<AiSector>,
    aiSector: AiSector,
    subSectorsList: List<List<AiSubSector>>,
    footer: (@Composable () -> Unit)? = null,
    switchAiTab: (AiSector) -> Unit,
    sentenceClickIndex: Int = -1,
    initFeedback: EvaluateFeedbackState? = null,
    feedbackClick: ((AiSector, EvaluateFeedback, Boolean) -> Unit)? = null,
    score: Int = -1,
) {
    if (subSectorsList.isEmpty()) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            aiSector.placeHolder?.invoke()
        }
        return
    }
    val listState = rememberLazyListState()
    val firstSize = subSectorsList.firstOrNull()?.size ?: 0
    val secondSize = subSectorsList.getOrNull(1)?.size ?: 0
    val thirdSize = subSectorsList.getOrNull(2)?.size ?: 0

    // 计算各个部分在LazyColumn中的实际起始位置
    // LazyColumn结构：Spacer + ScoreLayout + 第一部分items + 第一部分PraiseWidget + 点拨标题 + 第二部分items + 第二部分PraiseWidget + 加油站标题 + 第三部分items + 第三部分PraiseWidget + footer + Spacer
    val firstStartIndex = 2 // 第一个Spacer + ScoreLayout之后

    // 第二部分的起始位置：第一个Spacer + ScoreLayout + 第一部分items + 第一部分PraiseWidget + 点拨标题
    val secondTitleIndex = firstStartIndex + firstSize + (if (firstSize > 0) 1 else 0) // 点拨标题的位置
    val secondStartIndex = secondTitleIndex + (if (secondSize > 0) 1 else 0) // 第二部分items的起始位置

    // 第三部分的起始位置：前面所有 + 第二部分items + 第二部分PraiseWidget + 加油站标题
    val thirdTitleIndex = secondStartIndex + secondSize + (if (secondSize > 0) 1 else 0) // 加油站标题的位置
    val thirdStartIndex = thirdTitleIndex + (if (thirdSize > 0) 1 else 0) // 第三部分items的起始位置

    // 监听数据变化，重置滚动位置
    LaunchedEffect(key1 = aiSector) {
        when (aiSector) {
            AiSector.Evaluate -> {
                // 滚动到第一部分的开始
                if (firstSize > 0) {
                    listState.scrollToItem(firstStartIndex)
                }
            }
            AiSector.Dibble -> {
                // 滚动到第二部分的开始（点拨标题）
                if (secondSize > 0) {
                    listState.scrollToItem(secondTitleIndex) // 滚动到点拨标题
                }
            }
            AiSector.Petrol -> {
                // 滚动到第三部分的开始（加油站标题）
                if (thirdSize > 0) {
                    listState.scrollToItem(thirdTitleIndex) // 滚动到加油站标题
                }
            }
        }
    }
    LaunchedEffect(key1 = sentenceClickIndex) {
        if (sentenceClickIndex >= 0) {
            // 句子点击应该滚动到第二部分（点拨）的开始
            if (secondSize > 0) {
                listState.scrollToItem(secondTitleIndex) // 滚动到点拨标题
            }
        }
    }
    var lastSector = 0
    // 滚动监听：获取当前可视区域的第一个 item 索引
    LaunchedEffect(Unit) {
        snapshotFlow { listState.firstVisibleItemIndex }
            .collect { index ->
                // 找到该 index 对应的 AiSubSector
                val visibleItems = listState.layoutInfo.visibleItemsInfo
                if (visibleItems.isNotEmpty()) {
                    var sectorToSwitch: AiSector? = null
                    when {
                        // 在第一部分范围内（包括第一部分的items和PraiseWidget）
                        index < secondTitleIndex -> {
                            sectorToSwitch = sectorList.firstOrNull { it == AiSector.Evaluate }
                        }
                        // 在第二部分范围内（从点拨标题到第二部分PraiseWidget）
                        index < thirdTitleIndex -> {
                            sectorToSwitch = sectorList.firstOrNull { it == AiSector.Dibble }
                        }
                        // 在第三部分范围内（从加油站标题开始）
                        else -> {
                            sectorToSwitch = sectorList.firstOrNull { it == AiSector.Petrol }
                        }
                    }
                    // 防止频繁调用
                    if (sectorToSwitch != null && sectorList.indexOf(sectorToSwitch) != lastSector) {
                        switchAiTab(sectorToSwitch)
                        lastSector = sectorList.indexOf(sectorToSwitch)
                    }
                }
            }
    }
    LazyColumn(
        state = listState,
        verticalArrangement = Arrangement.spacedBy(20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        item {
            Spacer(modifier = Modifier.size(0.dp))
        }
        item {
            ScoreLayout(score)
        }
        subSectorsList.forEachIndexed { i, subSectors ->
            if (i == 1 && subSectors.isNotEmpty()) {
                item {
                    Row(
                        modifier = Modifier.fillMaxWidth().offset(x = (-50).dp),
                    ) {
                        Image(
                            modifier = Modifier.size(width = 166.dp, height = 36.dp),
                            painter = painterResource(id = R.drawable.icon_dibble_title),
                            contentDescription = null
                        )
                    }
                }
            } else if (i == 2 && subSectors.isNotEmpty()) {
                item {
                    Row(
                        modifier = Modifier.fillMaxWidth().offset(x = (-20).dp),
                    ) {
                        Image(
                            modifier = Modifier.size(width = 166.dp, height = 36.dp),
                            painter = painterResource(id = R.drawable.icon_petrol_title),
                            contentDescription = null
                        )
                    }
                }
            }
            if(subSectors.isNotEmpty()){
                itemsIndexed(subSectors) { index, item ->

                    when (item) {
                        is AiSubSector.History,
                        is AiSubSector.Reality,
                        is AiSubSector.RefinedSentence
                            -> {
                            AiSubSectorLayout(
                                aiSubSector = item,
                                backgroundColor = Color(0xFFF5F6FD),
                            )
                        }

                        else -> {
                            AiSubSectorLayout(
                                aiSubSector = item,
                            )
                        }
                    }
                }
            }
            if(subSectors.isNotEmpty()){
                item {
                    Box(modifier = Modifier.fillMaxWidth().offset(x = (-24).dp),
                      ) {
                        CompositionPraiseWidget(
                            selectedAiSector = sectorList[i],
                            initFeedback = initFeedback?.feedbackMap?.get(sectorList[i]),
                            needAnimation = initFeedback?.needAnimation ?: false,
                            feedbackClick = feedbackClick
                        )
                    }
                }
            }

        }
        if (footer != null) {
            item {
                footer()
            }
        }
        item {
            Spacer(modifier = Modifier.size(0.dp))
        }
    }
}

@Composable
fun AiSectorList(
    jobStatus: JobStatus,
    aiSector: AiSector,
    subSectors: List<AiSubSector>,
    footer: (@Composable () -> Unit)? = null,
    score: Int = -1,
) {
    if (jobStatus.isRunning() && aiSector is AiSector.Petrol) { // 仅加油站
        AiLoadingAnimation()
        return
    }
    if (subSectors.isEmpty()) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            aiSector.placeHolder?.invoke()
        }
        return
    }
    val listState = rememberLazyListState()
    // 监听数据变化，重置滚动位置
    LaunchedEffect(key1 = aiSector) {
        listState.scrollToItem(0) // 滚动到第 0 项
    }
    LazyColumn(
        state = listState,
        verticalArrangement = Arrangement.spacedBy(20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        item {
            Spacer(modifier = Modifier.size(0.dp))
        }
        item {
            ScoreLayout(score)
        }
        itemsIndexed(subSectors) { _, item ->
            GetAiSubSectorLayout(aiSector, item)
        }
        if (footer != null) {
            item {
                footer()
            }
        }
        item {
            Spacer(modifier = Modifier.size(0.dp))
        }
    }
}

@Composable
private fun GetAiSubSectorLayout(
    aiSector: AiSector,
    item: AiSubSector
) {
    when (aiSector) {
        AiSector.Evaluate -> { // 综合评价
            AiSubSectorLayout(
                aiSubSector = item,
            )
        }

        AiSector.Dibble -> { // 点拨
            AiSubSectorLayout(
                aiSubSector = item,
            )
        }

        AiSector.Petrol -> { // 加油站
            AiSubSectorLayout(
                aiSubSector = item,
                backgroundColor = Color(0xFFF5F6FD),
            )
        }
    }
}

// ----------------------------------------------------------------------
@Composable
fun CompositionAiTabLayout(
    selectedSector: AiSector,
    onClick: (AiSector) -> Unit,
    sectorList: List<AiSector>,
) {
    Row(
        modifier = Modifier.height(74.dp),
        horizontalArrangement = Arrangement.spacedBy(28.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        sectorList.forEach {
            CompositionSelectedIcon(
                modifier = Modifier
                    .width(147.dp)
                    .height(40.dp),
                normalRes = it.normalRes,
                selectedRes = it.selectedRes,
                isSelected = selectedSector == it,
                onClick = {
                    onClick(it)
                })
        }
    }
}

// ----------------------------------------------------------------------
// ----------------------------------------------------------------------
@Composable
private fun AiSubSectorLayout(
    aiSubSector: AiSubSector,
    backgroundColor: Color? = null,
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Image(
            modifier = Modifier,
            painter = painterResource(aiSubSector.iconRes),
            contentDescription = null
        )
        Spacer(modifier = Modifier.size(15.dp))
        when {
            aiSubSector.comments.isEmpty() -> {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(208.dp)
                        .background(
                            color = backgroundColor ?: aiSubSector.backgroundColor,
                            shape = RoundedCornerShape(CORNER_RADIUS)
                        )
                        .padding(20.dp),
                    contentAlignment = Alignment.Center
                ) {
                    aiSubSector.placeHolder?.invoke()
                }
            }

            aiSubSector is AiSubSector.Sentence -> { // 句子特殊处理
                aiSubSector.comments.forEachIndexed { index, aiSectorComment ->
                    if (index > 0) {
                        Spacer(modifier = Modifier.size(15.dp))
                    }
                    AiSubSectorItemWithContent(
                        comment = aiSectorComment,
                        backgroundColor = backgroundColor,
                    )
                }
            }

            else -> {
                aiSubSector.comments.forEachIndexed { index, aiSectorComment ->
                    if (index > 0) {
                        Spacer(modifier = Modifier.size(15.dp))
                    }
                    AiSubSectorItem(
                        comment = aiSectorComment,
                        backgroundColor = backgroundColor,
                    )
                }
            }
        }
    }
}

@Composable
private fun AiSubSectorItemWithContent(
    comment: AiSectorComment,
    backgroundColor: Color?,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = backgroundColor ?: comment.backgroundColor,
                shape = RoundedCornerShape(CORNER_RADIUS)
            )
            .padding(20.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxHeight()
        ) {
            AiCommentContent(
                content = "${comment.content}\n${comment.comment}"
            )
        }
    }
}


@Composable
private fun AiSubSectorItem(
    comment: AiSectorComment,
    backgroundColor: Color?,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = backgroundColor ?: comment.backgroundColor,
                shape = RoundedCornerShape(CORNER_RADIUS)
            )
            .padding(20.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxHeight()
        ) {
            AiCommentContent(
                content = comment.comment
            )
        }
    }
}

//@Composable
//fun AiCommentContentWithIndication(
//    indicator: Int,
//    indicatorColor: Color,
//    title: String,
//    content: String?,
//) {
//    ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
//        val (indicatorView, titleView, contentView) = createRefs()
//        CircleIndicatorIcon(
//            modifier = Modifier
//                .width(22.dp)
//                .height(22.dp)
//                .constrainAs(indicatorView) {
//                    top.linkTo(parent.top)
//                    start.linkTo(parent.start)
//                },
//            index = indicator,
//            indicatorColor = indicatorColor,
//        )
//        Text(
//            modifier = Modifier
//                .constrainAs(titleView) {
//                    top.linkTo(indicatorView.top)
//                    bottom.linkTo(indicatorView.bottom)
//                    start.linkTo(indicatorView.end, margin = 8.dp)
//                },
//            text = title,
//            fontSize = CompositionFonts.AiTitleFontSize,
//            fontWeight = FontWeight.Bold,
//            color = colorResource(R.color.text_black_color)
//        )
//        Spacer(modifier = Modifier.size(8.dp))
//        AiCommentContent(
//            modifier = Modifier
//                .constrainAs(contentView) {
//                    top.linkTo(titleView.bottom, margin = 8.dp)
//                    start.linkTo(anchor = titleView.start)
//                    end.linkTo(anchor = parent.end)
//                    width = Dimension.fillToConstraints
//                },
//            content = content
//        )
//    }
//}

@Composable
fun AiCommentContent(modifier: Modifier = Modifier, content: String?) {
    if (LocalInspectionMode.current) {
        // Preview 模式下显示占位符
        Text(
            text = content ?: "",
            modifier = modifier,
            fontSize = CompositionFonts.AiCommonFontSize,
            color = Color(0xFF17233D)
        )
        return
    }
    Box(modifier) {
        AndroidView(
            modifier = Modifier.wrapContentSize(),
            factory = { context ->
                MarkDownCommentView(context).apply {
                    id = R.id.ai_comment_tv
                    setPadding(0, 0, 0, 0)
                    setEditMode(false)
                }
            },
            update = { view ->
                view.setTextContent(content ?: "")
            }
        )
    }
}


@Preview
@Composable
fun AiSectorListPreview() {
    Box(modifier = Modifier.background(color = Color.White)) {
        AiSectorList(
            jobStatus = JobStatus.SUCCESS,
            aiSector = AiSector.Petrol,
            subSectors = CompositionMockData.mockAiSubSectorList()
        )
    }
}